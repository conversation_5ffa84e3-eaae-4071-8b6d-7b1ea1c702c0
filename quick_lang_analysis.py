import json
from collections import Counter
from langdetect import detect, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException

# Set seed for consistent results
DetectorFactory.seed = 0

def detect_language_safe(text):
    try:
        if not text or len(text.strip()) < 10:
            return 'unknown'
        return detect(text)
    except LangDetectException:
        return 'unknown'

def get_language_name(code):
    language_map = {
        'en': 'English', 'hi': 'Hindi', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
        'zh-cn': 'Chinese (Simplified)', 'zh-tw': 'Chinese (Traditional)', 'ja': 'Japanese',
        'ar': 'Arabic', 'pt': 'Portuguese', 'ru': 'Russian', 'it': 'Italian', 'ko': 'Korean',
        'nl': 'Dutch', 'sv': 'Swedish', 'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish',
        'pl': 'Polish', 'cs': 'Czech', 'hu': 'Hungarian', 'ro': 'Romanian', 'bg': 'Bulgarian',
        'hr': 'Croatian', 'sr': 'Serbian', 'sl': 'Slovenian', 'sk': 'Slovak', 'lt': 'Lithuanian',
        'lv': 'Latvian', 'et': 'Estonian', 'el': 'Greek', 'tr': 'Turkish', 'he': 'Hebrew',
        'ur': 'Urdu', 'bn': 'Bengali', 'ta': 'Tamil', 'te': 'Telugu', 'mr': 'Marathi',
        'gu': 'Gujarati', 'kn': 'Kannada', 'ml': 'Malayalam', 'pa': 'Punjabi', 'or': 'Oriya',
        'as': 'Assamese', 'ne': 'Nepali', 'si': 'Sinhala', 'my': 'Burmese', 'th': 'Thai',
        'vi': 'Vietnamese', 'id': 'Indonesian', 'ms': 'Malay', 'tl': 'Tagalog', 'sw': 'Swahili',
        'am': 'Amharic', 'yo': 'Yoruba', 'ig': 'Igbo', 'ha': 'Hausa', 'zu': 'Zulu',
        'af': 'Afrikaans', 'fa': 'Persian', 'ps': 'Pashto', 'ku': 'Kurdish', 'hy': 'Armenian',
        'ka': 'Georgian', 'az': 'Azerbaijani', 'kk': 'Kazakh', 'ky': 'Kyrgyz', 'tg': 'Tajik',
        'tk': 'Turkmen', 'uz': 'Uzbek', 'mn': 'Mongolian', 'bo': 'Tibetan', 'ug': 'Uyghur',
        'ca': 'Catalan', 'uk': 'Ukrainian', 'cy': 'Welsh', 'mk': 'Macedonian', 'so': 'Somali',
        'unknown': 'Unknown/Undetected'
    }
    return language_map.get(code, f'Unknown ({code})')

# Load and analyze
with open('1_filtered.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f'Dataset contains {len(data)} total records')
print('Analyzing sample of 3000 records for language detection...\n')

sample_data = data[:3000]
overall_languages = Counter()

for i, record in enumerate(sample_data):
    if i % 300 == 0:
        print(f'Processed {i} records...')
    
    # Combine instruction and output for detection
    combined_text = ''
    if 'instruction' in record:
        combined_text += record['instruction'] + ' '
    if 'output' in record:
        combined_text += record['output']
    
    if combined_text.strip():
        lang = detect_language_safe(combined_text)
        overall_languages[lang] += 1

print(f'Processed {len(sample_data)} records...')
print('\n' + '='*60)
print('LANGUAGES DETECTED IN YOUR DATASET')
print('='*60)

total = sum(overall_languages.values())
for lang_code, count in overall_languages.most_common():
    lang_name = get_language_name(lang_code)
    percentage = (count / total) * 100
    print(f'{lang_name:25} | {count:5} records | {percentage:5.1f}%')

unique_langs = [get_language_name(lang) for lang in overall_languages.keys() if lang != 'unknown']
print(f'\n📊 SUMMARY:')
print(f'Total unique languages detected: {len(unique_langs)}')
print(f'\n🌍 Complete list of languages in your dataset:')
print(', '.join(sorted(unique_langs)))
